["tests/test_multi_merchant.py::TestMerchantContext::test_merchant_context_manager", "tests/test_multi_merchant.py::TestMerchantManagement::test_create_merchant", "tests/test_multi_merchant.py::TestMerchantManagement::test_create_wechat_config", "tests/test_multi_merchant.py::TestMerchantMiddleware::test_invalid_merchant", "tests/test_multi_merchant.py::TestMerchantMiddleware::test_source_parameter_extraction", "tests/test_multi_merchant.py::TestMerchantService::test_get_merchant_config", "tests/test_multi_merchant.py::TestMerchantService::test_merchant_validation", "tests/test_multi_merchant.py::TestPaymentIsolation::test_payment_callback_routing", "tests/test_multi_merchant.py::TestUserIsolation::test_user_isolation_between_merchants"]