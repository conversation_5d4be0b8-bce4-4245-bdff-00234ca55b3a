{"tests/test_multi_merchant.py::TestMerchantService::test_get_merchant_config": true, "tests/test_multi_merchant.py::TestMerchantService::test_merchant_validation": true, "tests/test_multi_merchant.py::TestMerchantMiddleware::test_source_parameter_extraction": true, "tests/test_multi_merchant.py::TestMerchantMiddleware::test_invalid_merchant": true, "tests/test_multi_merchant.py::TestUserIsolation::test_user_isolation_between_merchants": true, "tests/test_multi_merchant.py::TestPaymentIsolation::test_payment_callback_routing": true, "tests/test_multi_merchant.py::TestMerchantManagement::test_create_merchant": true, "tests/test_multi_merchant.py::TestMerchantManagement::test_create_wechat_config": true, "tests/test_multi_merchant.py::TestMerchantContext::test_merchant_context_manager": true}